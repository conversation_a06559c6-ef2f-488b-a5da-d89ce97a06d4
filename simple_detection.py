#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单检测脚本 - 加载integrated_yolo_ocr_model.pt模型进行检测

使用示例:
python simple_detection.py

功能:
- 加载models/integrated_yolo_ocr_model.pt模型
- 对DaYuanTuZ_0.png图像进行检测
- 同时进行YOLO目标检测和OCR文字识别
"""

import os
import torch
import cv2
import time
import json
import numpy as np
from PIL import Image
from typing import List, Tuple, Dict

# 导入自定义模型类
try:
    from train import MultiTaskModel
    CUSTOM_MODEL_AVAILABLE = True
except ImportError:
    print("⚠️  无法导入自定义模型类")
    CUSTOM_MODEL_AVAILABLE = False

class StandaloneYOLOOCRModel:
    """
    独立的YOLO+OCR模型，不依赖外部YOLO文件
    """
    def __init__(self):
        self.detection_conf_threshold = 0.25
        self.ocr_confidence_threshold = 0.5
        self.ocr_engines = {}
        self.yolo_model = None
        self.init_ocr_engines()

    def init_ocr_engines(self):
        """初始化OCR引擎"""
        try:
            # 尝试初始化CnOCR
            try:
                from cnocr import CnOcr
                self.ocr_engines['cnocr'] = CnOcr(
                    rec_model_name='densenet_lite_136-gru',
                    det_model_name='db_resnet18',
                    use_gpu=torch.cuda.is_available()
                )
                print("✓ CnOCR引擎初始化成功")
            except Exception as e:
                print(f"✗ CnOCR引擎初始化失败: {e}")

            # 尝试初始化EasyOCR
            try:
                import easyocr
                self.ocr_engines['easyocr'] = easyocr.Reader(['ch_sim', 'en'], gpu=torch.cuda.is_available())
                print("✓ EasyOCR引擎初始化成功")
            except Exception as e:
                print(f"✗ EasyOCR引擎初始化失败: {e}")

        except Exception as e:
            print(f"❌ OCR引擎初始化失败: {e}")

    def load_from_checkpoint(self, checkpoint_path):
        """从检查点加载模型（完全独立，不依赖外部YOLO文件）"""
        try:
            checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)

            if 'model_config' in checkpoint:
                config = checkpoint['model_config']

                print(f"🔧 加载独立模型配置...")
                print(f"🚫 完全不使用外部YOLO文件")
                print(f"✅ 只使用集成模型内置权重进行检测")

                # 使用通用YOLO模型作为基础架构（仅用于结构，不用于权重）
                fallback_models = ['yolo11s.pt', 'yolov8n.pt', 'yolo11n.pt']
                yolo_loaded = False

                for fallback in fallback_models:
                    if os.path.exists(fallback):
                        print(f"🔧 使用 {fallback} 作为基础架构（仅结构，不用权重）")
                        from ultralytics import YOLO
                        self.yolo_model = YOLO(fallback)
                        yolo_loaded = True
                        break

                if not yolo_loaded:
                    print(f"❌ 无法找到基础YOLO模型文件用于架构")
                    print(f"💡 请确保存在以下任一文件: {fallback_models}")
                    return False

                # 恢复配置
                self.detection_conf_threshold = config.get('detection_conf_threshold', 0.25)
                self.ocr_confidence_threshold = config.get('ocr_confidence_threshold', 0.5)

                # 保存集成模型的权重（暂时不加载到YOLO模型中）
                self.integrated_weights = checkpoint.get('model_state_dict', {})

                print(f"✅ 独立模型加载成功")
                print(f"   🎯 检测阈值: {self.detection_conf_threshold}")
                print(f"   📝 OCR阈值: {self.ocr_confidence_threshold}")
                print(f"   🔧 OCR引擎: {list(self.ocr_engines.keys())}")
                print(f"   💾 集成权重数量: {len(self.integrated_weights)}")

                return True
            else:
                print(f"❌ 模型文件格式不正确")
                return False

        except Exception as e:
            print(f"❌ 加载模型失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def load_model(model_path="models/integrated_yolo_ocr_model.pt"):
    """
    加载整合的YOLO+OCR模型（独立版本）

    Args:
        model_path: 模型文件路径

    Returns:
        model: 加载的模型实例，失败返回None
    """
    print(f"🔧 正在加载独立模型: {model_path}")

    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return None

    try:
        # 创建独立模型实例
        model = StandaloneYOLOOCRModel()

        # 从检查点加载
        if model.load_from_checkpoint(model_path):
            print(f"✅ 独立模型加载成功")
            return model
        else:
            print(f"❌ 独立模型加载失败")
            return None

    except Exception as e:
        print(f"❌ 加载模型时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def detect_with_sliding_window(image, model, window_size=(640, 640), overlap_ratio=0.2, conf_threshold=0.25):
    """
    使用滑动窗口对大图像进行检测

    Args:
        image: 输入图像 (numpy array)
        model: 已加载的模型
        window_size: 窗口大小 (width, height)
        overlap_ratio: 重叠比例
        conf_threshold: 置信度阈值

    Returns:
        all_detections: 所有YOLO检测结果
        all_text_results: 所有OCR识别结果
    """
    h, w = image.shape[:2]
    window_w, window_h = window_size

    print(f"� 使用滑动窗口检测，图像尺寸: {w}x{h}, 窗口尺寸: {window_w}x{window_h}")

    # 计算步长
    step_w = int(window_w * (1 - overlap_ratio))
    step_h = int(window_h * (1 - overlap_ratio))

    all_detections = []
    all_text_results = []

    # 计算窗口数量
    windows_x = (w - window_w) // step_w + 1 if w > window_w else 1
    windows_y = (h - window_h) // step_h + 1 if h > window_h else 1
    total_windows = windows_x * windows_y

    print(f"📊 总共需要处理 {total_windows} 个窗口")

    window_count = 0

    # 滑动窗口检测
    for y in range(0, h - window_h + 1, step_h):
        for x in range(0, w - window_w + 1, step_w):
            window_count += 1

            # 确保窗口不超出图像边界
            x_end = min(x + window_w, w)
            y_end = min(y + window_h, h)
            x_start = max(0, x_end - window_w)
            y_start = max(0, y_end - window_h)

            # 提取窗口
            window = image[y_start:y_end, x_start:x_end]

            print(f"   处理窗口 {window_count}/{total_windows}: ({x_start},{y_start}) -> ({x_end},{y_end})")

            try:
                # 保存临时窗口图像用于YOLO检测
                temp_window_path = f"temp_window_{window_count}.jpg"
                cv2.imwrite(temp_window_path, window)

                # 1. YOLO检测
                if hasattr(model, 'yolo_model'):
                    yolo_results = model.yolo_model.predict(
                        temp_window_path,
                        conf=conf_threshold,
                        save=False,
                        verbose=False
                    )

                    # 处理YOLO检测结果
                    if len(yolo_results) > 0:
                        result = yolo_results[0]
                        if hasattr(result, 'boxes') and result.boxes is not None:
                            for box in result.boxes:
                                # 获取窗口内的坐标
                                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                                confidence = float(box.conf[0])
                                class_id = int(box.cls[0])

                                # 转换到原图坐标
                                detection = {
                                    'class_id': class_id,
                                    'confidence': confidence,
                                    'bbox': [float(x1 + x_start), float(y1 + y_start),
                                           float(x2 + x_start), float(y2 + y_start)]
                                }
                                all_detections.append(detection)

                # 2. OCR检测
                window_text_results = []

                # 检查模型类型并调用相应的OCR方法
                if hasattr(model, 'detect_text_with_all_ocr'):
                    # MultiTaskModel 类型
                    window_text_results = model.detect_text_with_all_ocr(window)
                elif hasattr(model, 'ocr_engines') and model.ocr_engines:
                    # 直接使用OCR引擎
                    try:
                        # 使用CnOCR
                        if 'cnocr' in model.ocr_engines:
                            # 转换为PIL图像
                            if len(window.shape) == 3:
                                window_pil = Image.fromarray(cv2.cvtColor(window, cv2.COLOR_BGR2RGB))
                            else:
                                window_pil = Image.fromarray(window)

                            cnocr_results = model.ocr_engines['cnocr'].ocr(window_pil)
                            for result in cnocr_results:
                                text = result.get('text', '')
                                confidence = result.get('score', 0.0)
                                if confidence > model.ocr_confidence_threshold and text.strip():
                                    position = result.get('position', [])
                                    if len(position) >= 4:
                                        x_coords = [point[0] for point in position]
                                        y_coords = [point[1] for point in position]
                                        bbox = [
                                            int(min(x_coords)), int(min(y_coords)),
                                            int(max(x_coords)), int(max(y_coords))
                                        ]
                                        window_text_results.append({
                                            'text': text,
                                            'confidence': confidence,
                                            'detection_bbox': bbox,
                                            'engine': 'cnocr'
                                        })

                        # 使用EasyOCR
                        if 'easyocr' in model.ocr_engines:
                            easyocr_results = model.ocr_engines['easyocr'].readtext(window)
                            for bbox, text, confidence in easyocr_results:
                                if text.strip() and confidence > model.ocr_confidence_threshold:
                                    x_coords = [point[0] for point in bbox]
                                    y_coords = [point[1] for point in bbox]
                                    x1, y1 = int(min(x_coords)), int(min(y_coords))
                                    x2, y2 = int(max(x_coords)), int(max(y_coords))

                                    window_text_results.append({
                                        'text': text,
                                        'confidence': confidence,
                                        'detection_bbox': [x1, y1, x2, y2],
                                        'engine': 'easyocr'
                                    })

                    except Exception as e:
                        print(f"   窗口OCR检测失败: {e}")

                # 转换OCR结果坐标到原图
                for text_result in window_text_results:
                    if 'detection_bbox' in text_result:
                        bbox = text_result['detection_bbox']
                        # 转换到原图坐标
                        text_result['detection_bbox'] = [
                            bbox[0] + x_start, bbox[1] + y_start,
                            bbox[2] + x_start, bbox[3] + y_start
                        ]
                        all_text_results.append(text_result)

                # 清理临时文件
                if os.path.exists(temp_window_path):
                    os.remove(temp_window_path)

            except Exception as e:
                print(f"   窗口 {window_count} 检测失败: {e}")
                # 清理临时文件
                temp_window_path = f"temp_window_{window_count}.jpg"
                if os.path.exists(temp_window_path):
                    os.remove(temp_window_path)
                continue

    print(f"✅ 滑动窗口检测完成")
    print(f"🎯 YOLO检测结果: {len(all_detections)} 个")
    print(f"📝 OCR识别结果: {len(all_text_results)} 段")

    return all_detections, all_text_results

def detect_image(model, image_path="DaYuanTuZ_0.png"):
    """
    对图像进行检测（使用滑动窗口处理大图像）

    Args:
        model: 已加载的模型
        image_path: 图像路径

    Returns:
        detections: YOLO检测结果
        text_results: OCR识别结果
    """
    print(f"📷 正在检测图像: {image_path}")

    # 检查图像文件
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return [], []

    # 加载图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ 无法读取图像: {image_path}")
        return [], []

    h, w = image.shape[:2]
    print(f"✅ 图像加载成功，尺寸: {w}x{h}")

    # 判断是否需要使用滑动窗口
    max_size = 1280  # 如果图像任一边超过这个尺寸，使用滑动窗口

    if w > max_size or h > max_size:
        print(f"🔍 图像较大，使用滑动窗口检测")
        return detect_with_sliding_window(
            image=image,
            model=model,
            window_size=(640, 640),
            overlap_ratio=0.2,
            conf_threshold=0.25
        )
    else:
        print(f"🔍 图像较小，使用整图检测")
        # 对于小图像，直接检测
        try:
            # YOLO目标检测
            yolo_detections = []
            if hasattr(model, 'yolo_model'):
                yolo_results = model.yolo_model.predict(
                    image_path,
                    conf=model.detection_conf_threshold,
                    save=False,
                    verbose=False
                )

                if len(yolo_results) > 0:
                    result = yolo_results[0]
                    if hasattr(result, 'boxes') and result.boxes is not None:
                        for box in result.boxes:
                            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                            confidence = float(box.conf[0])
                            class_id = int(box.cls[0])

                            yolo_detections.append({
                                'class_id': class_id,
                                'confidence': confidence,
                                'bbox': [float(x1), float(y1), float(x2), float(y2)]
                            })

            # OCR文字识别
            text_results = []
            if hasattr(model, 'detect_text_with_all_ocr'):
                text_results = model.detect_text_with_all_ocr(image)

            return yolo_detections, text_results

        except Exception as e:
            print(f"❌ 检测过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return [], []

def visualize_results(image, detections, text_results, save_path="detection_result.jpg"):
    """
    可视化检测结果
    
    Args:
        image: 原始图像
        detections: YOLO检测结果
        text_results: OCR识别结果
        save_path: 保存路径
    """
    result_image = image.copy()
    
    # 绘制YOLO检测框（绿色）
    for detection in detections:
        bbox = detection['bbox']
        confidence = detection['confidence']
        class_id = detection['class_id']
        
        x1, y1, x2, y2 = map(int, bbox)
        cv2.rectangle(result_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
        
        label = f"Class {class_id}: {confidence:.2f}"
        cv2.putText(result_image, label, (x1, y1-10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    
    # 绘制OCR文字框（蓝色）
    for text_result in text_results:
        if 'detection_bbox' in text_result:
            bbox = text_result['detection_bbox']
            text = text_result['text']
            confidence = text_result['confidence']
            
            x1, y1, x2, y2 = map(int, bbox)
            cv2.rectangle(result_image, (x1, y1), (x2, y2), (255, 0, 0), 2)
            
            # 显示文字（截断过长的文字）
            display_text = text[:20] + "..." if len(text) > 20 else text
            cv2.putText(result_image, display_text, (x1, y2+15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 0), 1)
    
    # 保存结果图像
    cv2.imwrite(save_path, result_image)
    print(f"💾 结果图像已保存到: {save_path}")
    
    return result_image

def main():
    """
    主函数
    """
    print("🚀 开始简单检测...")
    
    # 设置参数
    model_path = "models/integrated_yolo_ocr_model.pt"
    image_path = "DaYuanTuZ_0.png"
    
    print(f"📷 图像: {image_path}")
    print(f"🤖 模型: {model_path}")
    
    # 加载模型
    model = load_model(model_path)
    if model is None:
        print("❌ 模型加载失败，退出")
        return
    
    # 进行检测
    detections, text_results = detect_image(model, image_path)
    
    # 显示结果统计
    print(f"\n🎉 检测完成!")
    print(f"🎯 目标检测: {len(detections)} 个")
    print(f"📝 文字识别: {len(text_results)} 段")
    
    # 显示详细结果
    if detections:
        print(f"\n🎯 目标检测详情:")
        for i, detection in enumerate(detections[:5]):  # 只显示前5个
            class_id = detection.get('class_id', 0)
            confidence = detection.get('confidence', 0.0)
            bbox = detection.get('bbox', [0, 0, 0, 0])
            print(f"   {i+1}. 类别ID: {class_id}, 置信度: {confidence:.3f}, 位置: [{bbox[0]:.0f},{bbox[1]:.0f},{bbox[2]:.0f},{bbox[3]:.0f}]")
        if len(detections) > 5:
            print(f"   ... 还有 {len(detections) - 5} 个检测结果")
    
    if text_results:
        print(f"\n📝 文字识别详情:")
        for i, text_result in enumerate(text_results[:5]):  # 只显示前5个
            text = text_result.get('text', '')
            confidence = text_result.get('confidence', 0.0)
            engine = text_result.get('engine', 'unknown')
            print(f"   {i+1}. '{text}' (置信度: {confidence:.3f}, 引擎: {engine})")
        if len(text_results) > 5:
            print(f"   ... 还有 {len(text_results) - 5} 个文字结果")
    
    # 可视化并保存结果
    if detections or text_results:
        image = cv2.imread(image_path)
        if image is not None:
            visualize_results(image, detections, text_results, "simple_detection_result.jpg")
    
    print(f"\n✅ 检测完成!")

if __name__ == "__main__":
    main()
