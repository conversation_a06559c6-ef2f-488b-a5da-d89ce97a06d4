#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单检测脚本 - 加载integrated_yolo_ocr_model.pt模型进行检测

使用示例:
python simple_detection.py

功能:
- 加载models/integrated_yolo_ocr_model.pt模型
- 对DaYuanTuZ_0.png图像进行检测
- 同时进行YOLO目标检测和OCR文字识别
"""

import os
import torch
import cv2
import time
import json
import numpy as np
from PIL import Image
from typing import List, Tuple, Dict

# 导入自定义模型类
try:
    from train import MultiTaskModel, load_integrated_model
    CUSTOM_MODEL_AVAILABLE = True
except ImportError:
    print("⚠️  无法导入自定义模型类")
    CUSTOM_MODEL_AVAILABLE = False

def load_model(model_path="models/integrated_yolo_ocr_model.pt"):
    """
    加载整合的YOLO+OCR模型
    
    Args:
        model_path: 模型文件路径
        
    Returns:
        model: 加载的模型实例，失败返回None
    """
    print(f"🔧 正在加载模型: {model_path}")
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return None
    
    if not CUSTOM_MODEL_AVAILABLE:
        print("❌ 自定义模型类不可用")
        return None
    
    try:
        # 使用train.py中的load_integrated_model函数
        model = load_integrated_model(model_path)
        
        if model is not None:
            print(f"✅ 模型加载成功")
            return model
        else:
            print(f"❌ 模型加载失败")
            return None
            
    except Exception as e:
        print(f"❌ 加载模型时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def detect_image(model, image_path="DaYuanTuZ_0.png"):
    """
    对图像进行检测
    
    Args:
        model: 已加载的模型
        image_path: 图像路径
        
    Returns:
        detections: YOLO检测结果
        text_results: OCR识别结果
    """
    print(f"📷 正在检测图像: {image_path}")
    
    # 检查图像文件
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return [], []
    
    # 加载图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ 无法读取图像: {image_path}")
        return [], []
    
    print(f"✅ 图像加载成功，尺寸: {image.shape}")
    
    try:
        # YOLO目标检测
        print(f"🎯 正在进行YOLO目标检测...")
        yolo_detections = []
        
        if hasattr(model, 'yolo_model'):
            yolo_results = model.yolo_model.predict(
                image_path,
                conf=model.detection_conf_threshold,
                save=False,
                verbose=False
            )
            
            # 处理YOLO检测结果
            if len(yolo_results) > 0:
                result = yolo_results[0]
                if hasattr(result, 'boxes') and result.boxes is not None:
                    for box in result.boxes:
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = float(box.conf[0])
                        class_id = int(box.cls[0])
                        
                        yolo_detections.append({
                            'class_id': class_id,
                            'confidence': confidence,
                            'bbox': [float(x1), float(y1), float(x2), float(y2)]
                        })
        
        print(f"✅ YOLO检测完成，发现 {len(yolo_detections)} 个目标")
        
        # OCR文字识别
        print(f"📝 正在进行OCR文字识别...")
        text_results = []
        
        if hasattr(model, 'detect_text_with_all_ocr'):
            # 使用MultiTaskModel的OCR方法
            text_results = model.detect_text_with_all_ocr(image)
        elif hasattr(model, 'ocr_engines') and model.ocr_engines:
            # 直接使用OCR引擎
            try:
                # 使用CnOCR
                if 'cnocr' in model.ocr_engines:
                    # 转换为PIL图像
                    if len(image.shape) == 3:
                        image_pil = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
                    else:
                        image_pil = Image.fromarray(image)
                    
                    cnocr_results = model.ocr_engines['cnocr'].ocr(image_pil)
                    for result in cnocr_results:
                        text = result.get('text', '')
                        confidence = result.get('score', 0.0)
                        if confidence > model.ocr_confidence_threshold and text.strip():
                            position = result.get('position', [])
                            if len(position) >= 4:
                                x_coords = [point[0] for point in position]
                                y_coords = [point[1] for point in position]
                                bbox = [
                                    int(min(x_coords)), int(min(y_coords)),
                                    int(max(x_coords)), int(max(y_coords))
                                ]
                                text_results.append({
                                    'text': text,
                                    'confidence': confidence,
                                    'detection_bbox': bbox,
                                    'engine': 'cnocr'
                                })
                
                # 使用EasyOCR
                if 'easyocr' in model.ocr_engines:
                    easyocr_results = model.ocr_engines['easyocr'].readtext(image)
                    for bbox, text, confidence in easyocr_results:
                        if text.strip() and confidence > model.ocr_confidence_threshold:
                            x_coords = [point[0] for point in bbox]
                            y_coords = [point[1] for point in bbox]
                            x1, y1 = int(min(x_coords)), int(min(y_coords))
                            x2, y2 = int(max(x_coords)), int(max(y_coords))
                            
                            text_results.append({
                                'text': text,
                                'confidence': confidence,
                                'detection_bbox': [x1, y1, x2, y2],
                                'engine': 'easyocr'
                            })
                            
            except Exception as e:
                print(f"❌ OCR检测失败: {e}")
        
        print(f"✅ OCR识别完成，发现 {len(text_results)} 段文字")
        
        return yolo_detections, text_results
        
    except Exception as e:
        print(f"❌ 检测过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return [], []

def visualize_results(image, detections, text_results, save_path="detection_result.jpg"):
    """
    可视化检测结果
    
    Args:
        image: 原始图像
        detections: YOLO检测结果
        text_results: OCR识别结果
        save_path: 保存路径
    """
    result_image = image.copy()
    
    # 绘制YOLO检测框（绿色）
    for detection in detections:
        bbox = detection['bbox']
        confidence = detection['confidence']
        class_id = detection['class_id']
        
        x1, y1, x2, y2 = map(int, bbox)
        cv2.rectangle(result_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
        
        label = f"Class {class_id}: {confidence:.2f}"
        cv2.putText(result_image, label, (x1, y1-10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    
    # 绘制OCR文字框（蓝色）
    for text_result in text_results:
        if 'detection_bbox' in text_result:
            bbox = text_result['detection_bbox']
            text = text_result['text']
            confidence = text_result['confidence']
            
            x1, y1, x2, y2 = map(int, bbox)
            cv2.rectangle(result_image, (x1, y1), (x2, y2), (255, 0, 0), 2)
            
            # 显示文字（截断过长的文字）
            display_text = text[:20] + "..." if len(text) > 20 else text
            cv2.putText(result_image, display_text, (x1, y2+15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 0), 1)
    
    # 保存结果图像
    cv2.imwrite(save_path, result_image)
    print(f"💾 结果图像已保存到: {save_path}")
    
    return result_image

def main():
    """
    主函数
    """
    print("🚀 开始简单检测...")
    
    # 设置参数
    model_path = "models/integrated_yolo_ocr_model.pt"
    image_path = "DaYuanTuZ_0.png"
    
    print(f"📷 图像: {image_path}")
    print(f"🤖 模型: {model_path}")
    
    # 加载模型
    model = load_model(model_path)
    if model is None:
        print("❌ 模型加载失败，退出")
        return
    
    # 进行检测
    detections, text_results = detect_image(model, image_path)
    
    # 显示结果统计
    print(f"\n🎉 检测完成!")
    print(f"🎯 目标检测: {len(detections)} 个")
    print(f"📝 文字识别: {len(text_results)} 段")
    
    # 显示详细结果
    if detections:
        print(f"\n🎯 目标检测详情:")
        for i, detection in enumerate(detections[:5]):  # 只显示前5个
            class_id = detection.get('class_id', 0)
            confidence = detection.get('confidence', 0.0)
            bbox = detection.get('bbox', [0, 0, 0, 0])
            print(f"   {i+1}. 类别ID: {class_id}, 置信度: {confidence:.3f}")
        if len(detections) > 5:
            print(f"   ... 还有 {len(detections) - 5} 个检测结果")
    
    if text_results:
        print(f"\n📝 文字识别详情:")
        for i, text_result in enumerate(text_results[:5]):  # 只显示前5个
            text = text_result.get('text', '')
            confidence = text_result.get('confidence', 0.0)
            engine = text_result.get('engine', 'unknown')
            print(f"   {i+1}. '{text}' (置信度: {confidence:.3f}, 引擎: {engine})")
        if len(text_results) > 5:
            print(f"   ... 还有 {len(text_results) - 5} 个文字结果")
    
    # 可视化并保存结果
    if detections or text_results:
        image = cv2.imread(image_path)
        if image is not None:
            visualize_results(image, detections, text_results, "simple_detection_result.jpg")
    
    print(f"\n✅ 检测完成!")

if __name__ == "__main__":
    main()
